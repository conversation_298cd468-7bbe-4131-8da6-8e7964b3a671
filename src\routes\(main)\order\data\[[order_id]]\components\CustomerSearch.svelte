<script lang="ts">
	import { type Customer } from '$lib/schema/general';
	import UtilityModal from '$lib/utils/utility/UtilityModal.svelte';
	import {
		getUtilityModalState,
		setUtilityModalState
	} from '$lib/utils/utility/utilityModalState.svelte';
	import { getInvoiceState } from '../serviceOrder/InvoiceState.svelte';
	import Icon from '@iconify/svelte';

	setUtilityModalState();
	const utilityModalState = getUtilityModalState();
	const invoiceState = getInvoiceState();
</script>

<button
	class="btn btn-sm btn-outline btn-primary"
	onclick={() => utilityModalState.modal?.showModal()}
>
	Customer Terdaftar <Icon icon="mdi:account-search" />
</button>

<UtilityModal url="/customer">
	{#snippet title()}
		<h2 class="font-semibold">Pilih Customer</h2>
	{/snippet}

	{#snippet item({ item }: { item: Customer })}
		<button
			class="btn btn-outline mb-1 w-full"
			onclick={() => {
				invoiceState.order.customer = item;
				invoiceState.order.alamat = item.alamat;
				utilityModalState.modal?.close();
			}}
		>
			{item.nama} ({item.id_customer})
		</button>
	{/snippet}
</UtilityModal>
