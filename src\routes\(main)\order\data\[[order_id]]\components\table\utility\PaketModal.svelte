<script lang="ts">
	import type { Paket } from '$lib/schema/general';

	import {
		getUtilityModalState,
		setUtilityModalState
	} from '$lib/utils/utility/utilityModalState.svelte';

	import UtilityModal from '$lib/utils/utility/UtilityModal.svelte';
	import { getInvoiceState } from '../../../serviceOrder/InvoiceState.svelte';
	import { getToastState } from '$lib/utils/toast/ToastState.svelte';

	import Icon from '@iconify/svelte';

	setUtilityModalState();
	const utilityModalState = getUtilityModalState();
	const invoiceState = getInvoiceState();
	const toastState = getToastState();

	const groupIndex = $derived(
		invoiceState.service_order.findIndex((service) =>
			service.find((order) => order.kind === 'paket')
		)
	);

	const group = $derived(groupIndex === -1 ? [] : invoiceState.service_order[groupIndex]);

	interface IProps {
		onTable?: boolean;
	}
	const { onTable = false }: IProps = $props();
</script>

{#if onTable}
	<button
		class="btn btn-xs btn-outline btn-soft"
		onclick={() => utilityModalState.modal?.showModal()}
	>
		-- Ganti Paket -- <Icon icon="majesticons:color-swatch" />
	</button>
{:else}
	<button class="btn btn-outline w-full" onclick={() => utilityModalState.modal?.showModal()}>
		Paket
	</button>
{/if}

<UtilityModal url="/paket">
	{#snippet title()}
		<h2 class="font-semibold">Pilih Paket</h2>
	{/snippet}

	{#snippet item({ item }: { item: Paket })}
		{@const alreadySelected = group.find(
			(order) => order.kind === 'paket' && (order.data as Paket).id_paket === item.id_paket
		)}
		{@const tip =
			item.jasa_paket.map((jasa) => jasa.nama_jasa).join(', ') +
			' , ' +
			item.sparepart_paket.map((sparepart) => sparepart.nama_sparepart).join(', ')}
		<button
			class="btn btn-primary btn-sm btn-soft w-full justify-start"
			class:btn-disabled={alreadySelected}
			onclick={() => {
				if (alreadySelected) {
					toastState.add({
						message: 'paket sudah dipilih',
						type: 'error'
					});
					return;
				}

				const availableGroupIndex =
					groupIndex === -1
						? invoiceState.service_order.findIndex((service) => service.length === 0)
						: groupIndex;

				invoiceState.service_order[availableGroupIndex].push({
					kind: 'paket',
					data: item,
					qty: 1,
					harga: item.harga,
					montir: null
				});
			}}
		>
			<div class="tooltip grow" data-tip={tip}>
				<div class="flex grow items-center justify-between gap-2">
					<div>{item.nama_paket}</div>
					<div>({item.jasa_paket.length} Jasa, {item.sparepart_paket.length} Sparepart)</div>
				</div>
			</div>
		</button>
	{/snippet}
</UtilityModal>
