import type { Actions, PageServerLoad } from './$types';
import {
	CustomerSchema,
	KaryawanSchema,
	KendaraanSchema,
	type Customer,
	type Jasa,
	type Kendaraan,
	type Montir,
	type Paket,
	type Sparepart
} from '$lib/schema/general';

import {
	_Invoice,
	OrderJasa,
	OrderPaket,
	OrderSchema,
	OrderSparepart,
	type Invoice,
	type Order,
	type OrderEncoded
} from '$lib/schema/order';

import type { Service, CustomService } from './serviceOrder/InvoiceState.svelte';
import { fail, isActionFailure, type ActionFailure } from '@sveltejs/kit';
import { Effect, Schema } from 'effect';
import { launch, retrieve } from '$lib/utils/fetch';
import { decodeForm, type DecodeFormValueErrors } from '$lib/utils/validation';
import { statusTransaksi } from '$lib/schema/literal';
import { env } from '$env/dynamic/public';
import type { Mutable } from 'effect/Types';

export const load: PageServerLoad = async ({ fetch, params }) => {
	let invoice: Invoice = _Invoice;

	if (params.order_id) {
		const getInvoice = retrieve<Invoice>(fetch, `/invoice/${params.order_id}`);
		const _invoice = await Effect.runPromise(getInvoice);
		invoice = _invoice.data;
	}

	return { invoice };
};

type ServiceOrder = [Service[], Service[], Service[], Service[]];

let SVELTEKIT_FETCH: {
	(input: RequestInfo | URL, init?: RequestInit): Promise<Response>;
	(input: RequestInfo | URL, init?: RequestInit): Promise<Response>;
	(input: RequestInfo | URL, init?: RequestInit): Promise<Response>;
	(input: RequestInfo | URL, init?: RequestInit): Promise<Response>;
	(input: RequestInfo | URL, init?: RequestInit): Promise<Response>;
};
export const actions: Actions = {
	'submit:order': async ({ request, fetch, locals }) => {
		SVELTEKIT_FETCH = fetch;
		const data = await request.formData();

		///////////////////////////////////////

		const service_order = JSON.parse(data.get('service_order') as string) as ServiceOrder;

		const CHECKER_1 = checkServiceOrder(service_order);
		if (isActionFailure(CHECKER_1)) return CHECKER_1;

		///////////////////////////////////////

		const order = JSON.parse(data.get('order') as string) as Order;

		const CHECKER_2 = checkOrder(order);
		if (isActionFailure(CHECKER_2)) return CHECKER_2;

		///////////////////////////////////////

		const subtotal = Number(data.get('subtotal') as string);
		const discount_by_percentage = Number(data.get('discount_by_percentage') as string);
		const discount_by_value = Number(data.get('discount_by_value') as string);
		const ppn = Number(data.get('ppn') as string);
		const total = Number(data.get('total') as string);

		const CHECKER_3 = checkTotal(subtotal, total);
		if (isActionFailure(CHECKER_3)) return CHECKER_3;

		///////////////////////////////////////

		// CHECK IF CUSTOMER IS PRESENT //
		// IF NOT CREATE NEW CUSTOMER //

		if (order.customer.id_customer === '') {
			order.customer.alamat.jalan = order.alamat.jalan;
			const responseCustomer = await postCustomer(order.customer);
			if (isActionFailure(responseCustomer)) return responseCustomer;

			console.log(responseCustomer);

			if ('id_customer' in responseCustomer.data)
				order.customer.id_customer = responseCustomer.data.id_customer;
		}

		//////////////////////////////////////

		// CHECK IF VEHICLE IS PRESENT //
		// IF NOT CREATE NEW VEHICLE //

		if (order.kendaraan.id_kendaraan === '') {
			order.kendaraan.nomor_polisi = order.nomor_polisi;
			const responseVehicle = await postVehicle(order.kendaraan);
			if (isActionFailure(responseVehicle)) return responseVehicle;
		}
		//////////////////////////////////////

		// CREATE THE ORDER //

		const responseOrder = await postOrder(
			order,
			env.PUBLIC_ID_BENGKEL,
			locals.auth.id,
			discount_by_value
		);

		if (isActionFailure(responseOrder)) return responseOrder;

		///////////////////////////////////////

		// CREATE THE ORDER SERVICES //

		if ('nomor_order' in responseOrder.data) {
			const responseOrderServices = await postServiceOrder(
				service_order,
				responseOrder.data.nomor_order
			);

			if (isActionFailure(responseOrderServices)) return responseOrderServices;
		}

		return { message: 'Order created successfully.' };

		///////////////////////////////////////

		// CREATE THE INVOICE //

		const responseOrderData = responseOrder.data as { created_at: string; nomor_order: string };
		const postInvoice = launch(SVELTEKIT_FETCH, '/invoice', {
			method: 'POST',
			body: JSON.stringify({
				nomor_order: responseOrderData.nomor_order,
				id_karyawan: locals.auth.id,
				subtotal,
				total,
				status_transaksi: statusTransaksi[0], // Belum Dibayar
				keterangan: ''
			})
		});

		const responseInvoice = await Effect.runPromise(postInvoice);

		if (isActionFailure(responseInvoice)) return responseInvoice;

		return { order, service_order };
	}
};

const checkServiceOrder = (
	service_order: ServiceOrder
): ActionFailure<{ message: string }> | true => {
	if (service_order[0].length === 0)
		return fail(400, {
			message: 'Service order tidak boleh kosong.'
		});

	if (service_order.some((service) => service.some((item) => !item.montir)))
		return fail(400, {
			message: 'Montir harus diisi semua.'
		});

	return true;
};

const checkOrder = (order: Order): ActionFailure<{ errors: DecodeFormValueErrors }> | true => {
	const FormOrderSchema = Schema.Struct({
		...OrderSchema.omit('karyawan', 'customer', 'kendaraan').fields,
		customer: Schema.Struct({ ...CustomerSchema.omit('alamat', 'username', 'password').fields }),
		kendaraan: Schema.Struct({
			...KendaraanSchema.omit('pemilik', 'nomor_polisi', 'id_kendaraan').fields
		})
	});

	const decoded = decodeForm<typeof FormOrderSchema.Type, typeof FormOrderSchema.Encoded>(
		FormOrderSchema,
		order
	);

	if ('error' in decoded) return fail(400, { errors: decoded.errors });
	else return true;
};

const checkTotal = (subtotal: number, total: number): ActionFailure<{ message: string }> | true => {
	if (subtotal === 0 || total === 0)
		return fail(400, {
			message: 'Total tidak boleh kosong (Rp 0).'
		});

	return true;
};

const postOrder = async (
	order: Order,
	id_bengkel: string,
	id_karyawan: string,
	diskon: number
): Promise<
	ActionFailure<{ message: string }> | { data: { created_at: string; nomor_order: string } }
> => {
	const postOrder = launch(SVELTEKIT_FETCH, '/order', {
		method: 'POST',
		body: JSON.stringify({
			...order,
			id_bengkel,
			id_karyawan,
			id_customer: order.customer.id_customer,
			diskon
		})
	});

	return await Effect.runPromise(postOrder);
};

const postCustomer = async (
	customer: Mutable<Customer>
): Promise<ActionFailure<{ message: string }> | { data: { id_customer: string } }> => {
	const default_username =
		customer.nama
			.toLowerCase()
			.replace(/[^a-z0-9]/g, '_')
			.replace(/_+/g, '_')
			.replace(/_$/, '') +
		'_' +
		Math.floor(Math.random() * 100000);

	customer.username = default_username;
	const postCustomer = launch(SVELTEKIT_FETCH, '/customer', {
		method: 'POST',
		body: JSON.stringify(customer)
	});

	// TEMPORARY MESSAGE : /customer doesn't provide return id
	return await Effect.runPromise(postCustomer);
};

const postVehicle = async (
	vehicle: Kendaraan
): Promise<ActionFailure<{ message: string }> | { data: { nomor_polisi: string } }> => {
	const postVehicle = launch(SVELTEKIT_FETCH, '/kendaraan', {
		method: 'POST',
		body: JSON.stringify(vehicle)
	});

	return await Effect.runPromise(postVehicle);
};

const postServiceOrder = async (
	service_order: ServiceOrder,
	nomor_order: string
): Promise<ActionFailure | true> => {
	for (const services of service_order) {
		if (services.length === 0) continue;
		const kind = services[0].kind === 'custom' ? 'jasa' : services[0].kind;

		const payload = services.map((service) => {
			const baseData = {
				nomor_order,
				id_montir: (service.montir as Montir).id_montir,
				kuantitas: service.qty,
				harga: service.harga,
				keterangan: '',
				created_at: '',
				updated_at: null,
				deleted_at: null
			};

			let refactored: OrderJasa | OrderPaket | OrderSparepart;

			if (service.kind === 'paket') {
				refactored = {
					...baseData,
					id_order_paket: null, // This will be set by the database
					id_paket: (service.data as Paket).id_paket,
					nama_paket: (service.data as Paket).nama_paket,
					jasa_paket: (service.data as Paket).jasa_paket,
					sparepart_paket: (service.data as Paket).sparepart_paket
				} as OrderPaket;
			} else if (service.kind === 'jasa' || service.kind === 'custom') {
				refactored = {
					...baseData,
					id_order_jasa: null, // This will be set by the database
					id_jasa: service.kind === 'jasa' ? (service.data as Jasa).id_jasa : '',
					nama_jasa:
						service.kind === 'jasa'
							? (service.data as Jasa).nama_jasa
							: (service.data as CustomService).nama
				} as OrderJasa;
			} else {
				// sparepart
				refactored = {
					...baseData,
					id_order_sparepart: null, // This will be set by the database
					kode_sparepart: (service.data as Sparepart).kode_sparepart,
					nama_sparepart: (service.data as Sparepart).nama_sparepart
				} as OrderSparepart;
			}

			return refactored;
		});

		console.log(JSON.stringify(payload));

		const postOrderServices = launch(SVELTEKIT_FETCH, `/order/${kind}`, {
			method: 'POST',
			body: JSON.stringify(payload)
		});

		const responseOrderServices = await Effect.runPromise(postOrderServices);
		if (isActionFailure(responseOrderServices)) return responseOrderServices;
	}

	return true;
};
