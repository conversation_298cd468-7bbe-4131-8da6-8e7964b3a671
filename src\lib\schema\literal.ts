export const tipeCustomer = ['Individu', 'Perusahaan'];
export type TipeCustomer = (typeof tipeCustomer)[number];

export const jabatan = ['<PERSON><PERSON>', 'Manager', 'Administrator'];
export type Jabatan = (typeof jabatan)[number];

export const statusTransaksi = ['Belum Dibayar', 'Sudah Lunas'];
export type StatusTransaksi = (typeof statusTransaksi)[number];

export const jenisLayanan = ['<PERSON> Bengkel', '<PERSON> Tempat', '<PERSON>'] as const;
export type JenisLayanan = (typeof jenisLayanan)[number];

export const metodePembayaran = ['Cash', 'Transfer', 'Kredit'] as const;
export type MetodePembayaran = (typeof metodePembayaran)[number];

export const orderStatus = ['Antrian', 'Dikerjakan', 'Selesai', 'Void'] as const;
export type OrderStatus = (typeof orderStatus)[number];

export const statusMontir = ['Aktif', 'Nonaktif', 'Tunggu'] as const;
export type StatusMontir = (typeof statusMontir)[number];
