<script lang="ts">
	import { enhance } from '$app/forms';

	import Currency from '$lib/inputs/Currency.svelte';
	import ConfirmLoader from '$lib/utils/confirm/ConfirmLoader.svelte';

	import { formEnhancementWithValidation } from '$lib/utils/index';
	import { getDetailState } from './detailState.svelte';

	import { getConfirmState } from '$lib/utils/confirm/ConfirmState.svelte';
	import { getToastState } from '$lib/utils/toast/ToastState.svelte';
	import {
		getValidationErrorState,
		setValidationErrorState
	} from '$lib/utils/validation/ValidationErrorState.svelte';
	import FormField from '$lib/utils/formField/FormField.svelte';

	const confirmState = getConfirmState();
	const toastState = getToastState();

	const confirmation = (e: Event) => {
		confirmState.asking(
			{
				title: 'Konfirma<PERSON>',
				description: `<PERSON><PERSON><PERSON>h <PERSON>a yakin akan ${detailState.mode === 'add' ? 'menambahkan' : 'menyunting'} jasa ini?`,
				loader: 'action:jasa'
			},
			() => confirmState.submitForm(e)
		);
	};

	const detailState = getDetailState();
	let currencyReadonly = $derived(detailState.mode === 'view');

	setValidationErrorState();
	const validationErrorState = getValidationErrorState();
</script>

<dialog class="modal" bind:this={detailState.modal}>
	<div class="modal-box text-primary" class:bg-base-200={detailState.mode === 'view'}>
		<div class="mb-4 flex items-center justify-between gap-2 border-b border-b-gray-300 pb-2">
			<h3 class="text-lg font-bold">Detail Jasa</h3>
			<form method="dialog">
				<button
					class="btn btn-sm btn-circle btn-soft"
					onclick={() => (validationErrorState.errors = {})}>✕</button
				>
			</form>
		</div>

		{#if detailState.mode !== 'add'}
			<FormField
				name="id_jasa"
				label="ID Jasa"
				type="text"
				bind:mode={detailState.mode}
				bind:value={detailState.body.id_jasa}
				validation={false}
				disabled
			/>

			<br />
		{/if}

		<FormField
			name="nama_jasa"
			label="Nama Jasa"
			type="text"
			bind:mode={detailState.mode}
			bind:value={detailState.body.nama_jasa}
		/>

		<br />

		<label for="harga" class="floating-label">
			<span>Harga</span>
			<Currency
				name="harga"
				id="harga"
				class="w-full {detailState.mode === 'view' ? 'border-dashed' : ''}"
				bind:value={detailState.body.harga}
				bind:readonly={currencyReadonly}
			/>
		</label>

		<br />

		<FormField
			name="durasi_estimasi"
			label="Durasi Estimasi (Menit)"
			type="number"
			bind:mode={detailState.mode}
			bind:value={detailState.body.durasi_estimasi}
		/>

		<br />

		<FormField
			name="keterangan"
			label="Keterangan"
			type="textarea"
			bind:mode={detailState.mode}
			bind:value={detailState.body.keterangan}
			rows="3"
		/>

		<br />

		<form
			action="?/action:jasa"
			method="post"
			use:enhance={() =>
				formEnhancementWithValidation(validationErrorState, confirmState, toastState, () => {
					detailState.modal?.close();
				})}
		>
			<input type="hidden" name="jasa" value={JSON.stringify(detailState.body)} />
			<input type="hidden" name="mode" value={detailState.mode} />

			<button
				type="button"
				class="btn btn-primary w-full"
				onclick={(e) => confirmation(e)}
				class:hidden={detailState.mode === 'view'}
			>
				<ConfirmLoader name="action:jasa">Simpan</ConfirmLoader>
			</button>
		</form>
	</div>
</dialog>
