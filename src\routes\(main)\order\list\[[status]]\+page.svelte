<script lang="ts">
	import { goto } from '$app/navigation';
	import { shortcut } from '$lib/actions/shortcut.svelte';
	import PaginationUsingParam from '$lib/table/Pagination_Using_Param.svelte';
	import SearchUsingParam from '$lib/table/Search_Using_Param.svelte';
	import Table from '$lib/table/Table.svelte';
	import Icon from '@iconify/svelte';

	const { data } = $props();

	$inspect(data);
</script>

<svelte:window
	use:shortcut={[
		{ key: 'escape', callback: () => goto('/order') },
		{ key: 'enter', callback: () => goto('/order/data') }
	]}
/>

<div class="flex justify-around gap-2">
	<a href="/order" class="text-nowrap">
		<button class="btn btn-primary btn-sm btn-outline">
			<Icon icon="mdi:arrow-left" /> Kembali
		</button>
	</a>

	<SearchUsingParam placeholder="Search..." />

	<a href="/order/data" class="text-nowrap">
		<button class="btn btn-primary btn-sm btn-outline">
			<Icon icon="mdi:plus" /> Tambah Order
		</button>
	</a>

	<button class="btn btn-primary btn-sm btn-outline">
		<Icon icon="mdi:sort-alphabetical-ascending" />
		Sort By Ascending
	</button>
</div>

<br />

<Table table_header={data.tableHeader} table_data={data.list}>
	{#snippet custom({ header, body })}
		{#if header === 'Nama Pelanggan'}
			<div>{body.customer.nama}</div>
		{:else if header === 'Status'}
			{#if body.status === 'Antrian'}
				<div class="badge badge-primary text-[.75em] font-semibold text-white uppercase">
					{body.status}
				</div>
			{:else if body.status === 'Dikerjakan'}
				<div class="badge badge-warning text-[.75em] font-semibold uppercase">{body.status}</div>
			{:else if body.status === 'Selesai'}
				<div class="badge badge-success text-[.75em] font-semibold uppercase">{body.status}</div>
			{:else if body.status === 'Void'}
				<div class="badge badge-soft text-[.75em] font-semibold text-gray-600 uppercase">
					{body.status}
				</div>
			{/if}
		{:else if header === 'Actions'}
			<div class="flex items-center gap-2">
				{#if body.status === 'Antrian'}
					<button class="btn btn-sm btn-outline uppercase">
						<Icon icon="ri:delete-bin-6-fill" />
						hapus
					</button>

					<button class="btn btn-sm btn-outline uppercase">
						<Icon icon="ri:check-fill" />
						selanjutnya
					</button>
				{:else if body.status === 'Dikerjakan'}
					<button class="btn btn-sm btn-outline uppercase">
						<Icon icon="ri:pencil-fill" />
						edit
					</button>

					<button class="btn btn-sm btn-outline uppercase">
						<Icon icon="ri:printer-fill" />
						cetak invoice
					</button>
				{:else if body.status === 'Selesai'}
					{#if body.nomor_invoice}
						<a href="/invoice/{body.nomor_invoice}">
							<button class="btn btn-sm btn-outline uppercase">
								<Icon icon="ri:external-link-line" />
								lihat invoice
							</button>
						</a>
					{/if}

					<button class="btn btn-sm btn-outline uppercase">
						<Icon icon="ri:close-large-fill" />
						void
					</button>
				{/if}
			</div>
		{/if}
	{/snippet}
</Table>

<br />

<PaginationUsingParam total_content={10} />
