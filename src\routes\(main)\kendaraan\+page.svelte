<script lang="ts">
	import PaginationUsingParam from '$lib/table/Pagination_Using_Param.svelte';
	import SearchUsingParam from '$lib/table/Search_Using_Param.svelte';
	import Table from '$lib/table/Table.svelte';
	import Icon from '@iconify/svelte';

	import { getDetailState, setDetailState } from './components/detailState.svelte';
	import DetailModal from './components/DetailModal.svelte';
	import DeleteKendaraan from './components/DeleteKendaraan.svelte';
	import { setToastState } from '$lib/utils/toast/ToastState.svelte';

	const { data } = $props();

	setDetailState();
	const detailState = getDetailState();
</script>

<div class="flex justify-around gap-2">
	<button
		class="btn btn-primary btn-sm btn-outline"
		onclick={() => {
			detailState.addNewBody();
			detailState.mode = 'add';
			detailState.modal?.showModal();
		}}
	>
		<Icon icon="mdi:plus" /> Tambah Kendaraan
	</button>

	<SearchUsingParam placeholder="Search..." />

	<button class="btn btn-primary btn-sm btn-outline">
		<Icon icon="mdi:sort-alphabetical-ascending" />
		Sort By Ascending
	</button>
</div>

<br />

<Table
	table_header={[
		['numbering', 'No.'],
		['id_kendaraan', 'ID Kendaraan'],
		['nama_kendaraan', 'Nama Kendaraan'],
		['nomor_polisi', 'Nomor Polisi'],
		['custom', 'Pemilik'],
		['custom', 'CC/Tahun'],
		['keterangan', 'Keterangan'],
		['custom', 'Actions']
	]}
	table_data={data.list}
>
	{#snippet custom({ header, body })}
		{#if header === 'Actions'}
			<div class="flex items-center gap-2">
				<div class="tooltip" data-tip="Lihat Detail">
					<button
						class="btn btn-sm btn-outline btn-primary uppercase"
						onclick={() => {
							detailState.mode = 'view';
							detailState.body = body;
							// Convert partial customer to full customer for selectedCustomer
							detailState.selectedCustomer = {
								...body.pemilik,
								alamat: {
									jalan: '',
									'rt/rw': '',
									kelurahan: '',
									kecamatan: '',
									kota: '',
									provinsi: ''
								},
								username: '',
								password: ''
							};
							detailState.modal?.showModal();
						}}
					>
						<Icon icon="mdi:open-in-new" />
					</button>
				</div>

				<div class="tooltip" data-tip="Edit">
					<button
						class="btn btn-sm btn-outline btn-primary uppercase"
						onclick={() => {
							detailState.mode = 'edit';
							detailState.body = body;
							// Convert partial customer to full customer for selectedCustomer
							detailState.selectedCustomer = {
								...body.pemilik,
								alamat: {
									jalan: '',
									'rt/rw': '',
									kelurahan: '',
									kecamatan: '',
									kota: '',
									provinsi: ''
								},
								username: '',
								password: ''
							};
							detailState.modal?.showModal();
						}}
					>
						<Icon icon="mdi:pencil" />
					</button>
				</div>

				<DeleteKendaraan id={body.id_kendaraan} />
			</div>
		{:else if header === 'Pemilik'}
			{body.pemilik?.nama || '-'}
		{:else if header === 'CC/Tahun'}
			{body.cc_kendaraan}cc / {body.tahun_kendaraan}
		{/if}
	{/snippet}
</Table>

<br />

<div class="flex justify-end">
	<PaginationUsingParam total_content={data.total_rows} />
</div>

<DetailModal />
