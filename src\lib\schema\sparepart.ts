import { Schema } from 'effect';

export const SatuanSchema = Schema.Struct({
	id: Schema.Number.annotations({
		message: () => 'Kode Satuan Tidak Boleh Kosong'
	}),

	nama_satuan: Schema.NonEmptyString.annotations({
		message: () => '<PERSON>a Satuan Tidak Boleh Kosong'
	}),

	created_at: Schema.String,
	updated_at: Schema.NullOr(Schema.String),
	deleted_at: Schema.NullOr(Schema.String)
});

export interface Satuan extends Schema.Schema.Type<typeof SatuanSchema> {}
export interface SatuanEncoded extends Schema.Schema.Encoded<typeof SatuanSchema> {}

export const KategoriSparepartSchema = Schema.Struct({
	id_kategori_sparepart: Schema.NonEmptyString.annotations({
		message: () => 'Kode Kategori Tidak Boleh Kosong'
	}),
	nama_kategori: Schema.NonEmptyString,

	created_at: Schema.String,
	updated_at: Schema.NullOr(Schema.String),
	deleted_at: Schema.NullOr(Schema.String)
});

export interface KategoriSparepart extends Schema.Schema.Type<typeof KategoriSparepartSchema> {}
export interface KategoriSparepartEncoded
	extends Schema.Schema.Encoded<typeof KategoriSparepartSchema> {}

export const KartuStokSchema = Schema.Struct({
	id_kartustok: Schema.Number,
	kode_sparepart: Schema.String,
	id_bengkel: Schema.String,
	id_order: Schema.String,

	masuk: Schema.Number,
	keluar: Schema.Number,
	stok_barang: Schema.Number,

	keterangan: Schema.NullOr(Schema.String),

	created_at: Schema.String,
	updated_at: Schema.NullOr(Schema.String),
	deleted_at: Schema.NullOr(Schema.String)
});

export interface KartuStok extends Schema.Schema.Type<typeof KartuStokSchema> {}
export interface KartuStokEncoded extends Schema.Schema.Encoded<typeof KartuStokSchema> {}

export const StokOpnameSchema = Schema.Struct({
	id_bengkel: Schema.String,
	id_karyawan: Schema.String,
	id_kartuStok: Schema.String,

	id_stokOpname: Schema.String,

	stok_sistem: Schema.Number,
	stok_fisik: Schema.Number,
	selisih: Schema.Number,

	keterangan: Schema.NullOr(Schema.String),

	created_at: Schema.String,
	updated_at: Schema.NullOr(Schema.String),
	deleted_at: Schema.NullOr(Schema.String)
});

export interface StockOpname extends Schema.Schema.Type<typeof StokOpnameSchema> {}
export interface StockOpnameEncoded extends Schema.Schema.Encoded<typeof StokOpnameSchema> {}

//
