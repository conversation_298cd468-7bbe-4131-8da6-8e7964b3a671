<script lang="ts">
	import { enhance } from '$app/forms';
	import Icon from '@iconify/svelte';
	import type { Customer } from '$lib/schema/general';

	import ConfirmLoader from '$lib/utils/confirm/ConfirmLoader.svelte';
	import { formEnhancementWithValidation } from '$lib/utils/index';
	import { getDetailState } from './detailState.svelte';

	import { getConfirmState } from '$lib/utils/confirm/ConfirmState.svelte';
	import { getToastState } from '$lib/utils/toast/ToastState.svelte';
	import {
		getValidationErrorState,
		setValidationErrorState
	} from '$lib/utils/validation/ValidationErrorState.svelte';
	import ValidationError from '$lib/utils/validation/ValidationError.svelte';

	import UtilityModal from '$lib/utils/utility/UtilityModal.svelte';
	import {
		getUtilityModalState,
		setUtilityModalState
	} from '$lib/utils/utility/utilityModalState.svelte';

	setUtilityModalState();
	const utilityModalState = getUtilityModalState();

	const confirmState = getConfirmState();
	const toastState = getToastState();

	setValidationErrorState();
	const validationErrorState = getValidationErrorState();

	const detailState = getDetailState();

	const confirmation = (e: Event) => {
		confirmState.asking(
			{
				title: 'Konfirmasi',
				description: `Apakah Anda yakin akan ${detailState.mode === 'add' ? 'menambahkan' : 'menyunting'} kendaraan ini?`,
				loader: 'action:kendaraan'
			},
			() => confirmState.submitForm(e)
		);
	};

	// Update pemilik when customer is selected
	$effect(() => {
		if (detailState.selectedCustomer) {
			detailState.body.pemilik = {
				id_customer: detailState.selectedCustomer.id_customer,
				nama: detailState.selectedCustomer.nama,
				no_telp: detailState.selectedCustomer.no_telp,
				tipe_customer: detailState.selectedCustomer.tipe_customer,
				keterangan: detailState.selectedCustomer.keterangan,
				created_at: detailState.selectedCustomer.created_at,
				updated_at: detailState.selectedCustomer.updated_at,
				deleted_at: detailState.selectedCustomer.deleted_at
			};
		}
	});
</script>

<UtilityModal url="/customer">
	{#snippet title()}
		<h2 class="font-semibold">Pilih Customer</h2>
	{/snippet}

	{#snippet item({ item }: { item: Customer })}
		{@const alreadySelected = detailState.selectedCustomer?.id_customer === item.id_customer}
		<button
			class="btn btn-primary btn-sm btn-soft w-full justify-start"
			class:btn-disabled={alreadySelected}
			onclick={() => {
				if (alreadySelected) {
					toastState.add({
						message: 'Customer sudah dipilih',
						type: 'error'
					});
					return;
				}
				detailState.selectedCustomer = item;
				utilityModalState.modal?.close();
			}}
		>
			{item.nama} <span class="font-light">({item.id_customer})</span>
		</button>
	{/snippet}
</UtilityModal>

<dialog class="modal" bind:this={detailState.modal}>
	<div class="modal-box text-primary" class:bg-base-200={detailState.mode === 'view'}>
		<div class="mb-4 flex items-center justify-between gap-2 border-b border-b-gray-300 pb-2">
			<h3 class="text-lg font-bold">Detail Kendaraan</h3>
			<form method="dialog">
				<button
					class="btn btn-sm btn-circle btn-soft"
					onclick={() => (validationErrorState.errors = {})}>✕</button
				>
			</form>
		</div>

		<label for="id_kendaraan" class="floating-label mb-4" class:hidden={detailState.mode === 'add'}>
			<span>ID Kendaraan</span>
			<input
				type="text"
				name="id_kendaraan"
				id="id_kendaraan"
				class="input w-full"
				bind:value={detailState.body.id_kendaraan}
				placeholder="ID Kendaraan"
				disabled
			/>
		</label>

		<label for="nama_kendaraan" class="floating-label">
			<span>Nama Kendaraan</span>
			<input
				type="text"
				name="nama_kendaraan"
				id="nama_kendaraan"
				class="input w-full"
				bind:value={detailState.body.nama_kendaraan}
				placeholder="Nama Kendaraan"
				readonly={detailState.mode === 'view'}
				class:border-dashed={detailState.mode === 'view'}
			/>
			<ValidationError name="nama_kendaraan" />
		</label>

		<br />

		<label for="nomor_polisi" class="floating-label">
			<span>Nomor Polisi</span>
			<input
				type="text"
				name="nomor_polisi"
				id="nomor_polisi"
				class="input w-full"
				bind:value={detailState.body.nomor_polisi}
				placeholder="Nomor Polisi"
				readonly={detailState.mode === 'view'}
				class:border-dashed={detailState.mode === 'view'}
			/>
			<ValidationError name="nomor_polisi" />
		</label>

		<br />

		<div class="floating-label">
			<span>Pemilik</span>
			<div class="flex gap-2">
				<input
					type="text"
					class="input w-full"
					value={detailState.selectedCustomer?.nama || detailState.body.pemilik?.nama || ''}
					placeholder="Pilih Customer"
					readonly
					class:border-dashed={detailState.mode === 'view'}
				/>
				{#if detailState.mode !== 'view'}
					<button
						type="button"
						class="btn btn-primary btn-sm"
						onclick={() => utilityModalState.modal?.showModal()}
					>
						<Icon icon="mdi:account-search" />
					</button>
				{/if}
			</div>
			<ValidationError name="pemilik" />
		</div>

		<br />

		<div class="grid grid-cols-2 gap-4">
			<label for="cc_kendaraan" class="floating-label">
				<span>CC Kendaraan</span>
				<input
					type="number"
					name="cc_kendaraan"
					id="cc_kendaraan"
					class="input w-full"
					bind:value={detailState.body.cc_kendaraan}
					placeholder="CC Kendaraan"
					readonly={detailState.mode === 'view'}
					class:border-dashed={detailState.mode === 'view'}
					min="50"
				/>
				<ValidationError name="cc_kendaraan" />
			</label>

			<br />

			<label for="tahun_kendaraan" class="floating-label">
				<span>Tahun Kendaraan</span>
				<input
					type="number"
					name="tahun_kendaraan"
					id="tahun_kendaraan"
					class="input w-full"
					bind:value={detailState.body.tahun_kendaraan}
					placeholder="Tahun Kendaraan"
					readonly={detailState.mode === 'view'}
					class:border-dashed={detailState.mode === 'view'}
					min="1900"
					max={new Date().getFullYear() + 1}
				/>
				<ValidationError name="tahun_kendaraan" />
			</label>
		</div>

		<br />

		<label for="keterangan" class="floating-label">
			<span>Keterangan</span>
			<textarea
				name="keterangan"
				id="keterangan"
				class="textarea w-full"
				bind:value={detailState.body.keterangan}
				placeholder="Keterangan"
				readonly={detailState.mode === 'view'}
				class:border-dashed={detailState.mode === 'view'}
				rows="3"
			></textarea>
			<ValidationError name="keterangan" />
		</label>

		<br />

		<form
			action="?/action:kendaraan"
			method="post"
			use:enhance={() =>
				formEnhancementWithValidation(validationErrorState, confirmState, toastState, () => {
					detailState.modal?.close();
				})}
		>
			<input type="hidden" name="kendaraan" value={JSON.stringify(detailState.body)} />
			<input type="hidden" name="mode" value={detailState.mode} />

			<button
				type="button"
				class="btn btn-primary w-full"
				onclick={(e) => confirmation(e)}
				class:hidden={detailState.mode === 'view'}
			>
				<ConfirmLoader name="action:kendaraan">Simpan</ConfirmLoader>
			</button>
		</form>
	</div>
</dialog>
