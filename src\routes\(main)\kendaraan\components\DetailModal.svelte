<script lang="ts">
	import { enhance } from '$app/forms';
	import Icon from '@iconify/svelte';
	import type { Customer } from '$lib/schema/general';

	import ConfirmLoader from '$lib/utils/confirm/ConfirmLoader.svelte';
	import { formEnhancementWithValidation } from '$lib/utils/index';
	import { getDetailState } from './detailState.svelte';

	import { getConfirmState } from '$lib/utils/confirm/ConfirmState.svelte';
	import { getToastState } from '$lib/utils/toast/ToastState.svelte';
	import {
		getValidationErrorState,
		setValidationErrorState
	} from '$lib/utils/validation/ValidationErrorState.svelte';
	import FormField from '$lib/utils/formField/FormField.svelte';

	import UtilityModal from '$lib/utils/utility/UtilityModal.svelte';
	import {
		getUtilityModalState,
		setUtilityModalState
	} from '$lib/utils/utility/utilityModalState.svelte';

	setUtilityModalState();
	const utilityModalState = getUtilityModalState();

	const confirmState = getConfirmState();
	const toastState = getToastState();

	setValidationErrorState();
	const validationErrorState = getValidationErrorState();

	const detailState = getDetailState();

	const confirmation = (e: Event) => {
		confirmState.asking(
			{
				title: 'Konfirmasi',
				description: `Apakah Anda yakin akan ${detailState.mode === 'add' ? 'menambahkan' : 'menyunting'} kendaraan ini?`,
				loader: 'action:kendaraan'
			},
			() => confirmState.submitForm(e)
		);
	};
</script>

<UtilityModal url="/customer">
	{#snippet title()}
		<h2 class="font-semibold">Pilih Customer</h2>
	{/snippet}

	{#snippet item({ item }: { item: Customer })}
		{@const alreadySelected = detailState.body.pemilik?.id_customer === item.id_customer}
		<button
			class="btn btn-primary btn-sm btn-soft w-full justify-start"
			class:btn-disabled={alreadySelected}
			onclick={() => {
				if (alreadySelected) {
					toastState.add({
						message: 'Customer sudah dipilih',
						type: 'error'
					});
					return;
				}
				detailState.body.pemilik = item;
				utilityModalState.modal?.close();
			}}
		>
			{item.nama} <span class="font-light">({item.id_customer})</span>
		</button>
	{/snippet}
</UtilityModal>

<dialog class="modal" bind:this={detailState.modal}>
	<div class="modal-box text-primary" class:bg-base-200={detailState.mode === 'view'}>
		<div class="mb-4 flex items-center justify-between gap-2 border-b border-b-gray-300 pb-2">
			<h3 class="text-lg font-bold">Detail Kendaraan</h3>
			<form method="dialog">
				<button
					class="btn btn-sm btn-circle btn-soft"
					onclick={() => (validationErrorState.errors = {})}>✕</button
				>
			</form>
		</div>

		<FormField
			name="nama_kendaraan"
			label="Nama Kendaraan"
			type="text"
			bind:mode={detailState.mode}
			bind:value={detailState.body.nama_kendaraan}
		/>

		<br />

		<FormField
			name="nomor_polisi"
			label="Nomor Polisi"
			type="text"
			bind:mode={detailState.mode}
			bind:value={detailState.body.nomor_polisi}
		/>

		<br />

		<div class="floating-label">
			<span>Pemilik</span>
			<div class="flex items-center gap-2">
				<input
					type="text"
					class="input w-full"
					value={detailState.body.pemilik?.nama || ''}
					placeholder="Pilih Customer"
					readonly
					class:border-dashed={detailState.mode === 'view'}
				/>
				{#if detailState.mode !== 'view'}
					<button
						type="button"
						class="btn btn-primary btn-sm"
						onclick={() => utilityModalState.modal?.showModal()}
					>
						<Icon icon="mdi:account-search" />
					</button>
				{/if}
			</div>
		</div>

		<br />

		<div class="grid grid-cols-2 gap-4">
			<FormField
				name="cc_kendaraan"
				label="CC Kendaraan"
				type="number"
				bind:mode={detailState.mode}
				bind:value={detailState.body.cc_kendaraan}
				min="50"
			/>

			<FormField
				name="tahun_kendaraan"
				label="Tahun Kendaraan"
				type="number"
				bind:mode={detailState.mode}
				bind:value={detailState.body.tahun_kendaraan}
				min="1900"
				max={new Date().getFullYear() + 1}
			/>
		</div>

		<br />

		<FormField
			name="keterangan"
			label="Keterangan"
			type="textarea"
			bind:mode={detailState.mode}
			bind:value={detailState.body.keterangan}
			rows="3"
		/>

		<br />

		<form
			action="?/action:kendaraan"
			method="post"
			use:enhance={() =>
				formEnhancementWithValidation(validationErrorState, confirmState, toastState, () => {
					detailState.modal?.close();
				})}
		>
			<input type="hidden" name="kendaraan" value={JSON.stringify(detailState.body)} />
			<input type="hidden" name="mode" value={detailState.mode} />

			<button
				type="button"
				class="btn btn-primary w-full"
				onclick={(e) => confirmation(e)}
				class:hidden={detailState.mode === 'view'}
			>
				<ConfirmLoader name="action:kendaraan">Simpan</ConfirmLoader>
			</button>
		</form>
	</div>
</dialog>
