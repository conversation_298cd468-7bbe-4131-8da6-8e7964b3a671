<script lang="ts">
	import { type Kendaraan } from '$lib/schema/general';
	import UtilityModal from '$lib/utils/utility/UtilityModal.svelte';
	import {
		getUtilityModalState,
		setUtilityModalState
	} from '$lib/utils/utility/utilityModalState.svelte';
	import { getInvoiceState } from '../serviceOrder/InvoiceState.svelte';
	import Icon from '@iconify/svelte';

	setUtilityModalState();
	const utilityModalState = getUtilityModalState();
	const invoiceState = getInvoiceState();
</script>

<button
	class="btn btn-sm btn-outline btn-primary"
	onclick={() => utilityModalState.modal?.showModal()}
>
	Kendaraan Terdaftar <Icon icon="mdi:car-search" />
</button>

<UtilityModal url="/kendaraan">
	{#snippet title()}
		<h2 class="font-semibold"><PERSON><PERSON><PERSON></h2>
	{/snippet}

	{#snippet item({ item }: { item: Kendaraan })}
		<button
			class="btn btn-outline mb-1 w-full"
			onclick={() => {
				invoiceState.order.kendaraan = item;
				invoiceState.order.nomor_polisi = item.nomor_polisi;

				utilityModalState.modal?.close();
			}}
		>
			{item.nama_kendaraan} ({item.nomor_polisi})
		</button>
	{/snippet}
</UtilityModal>
