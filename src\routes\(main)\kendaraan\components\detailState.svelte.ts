import type { Kendaraan, Customer } from '$lib/schema/general';
import { getContext, setContext } from 'svelte';

export interface DetailState {
	modal: HTMLDialogElement | undefined;
	body: Kendaraan;
	mode: 'add' | 'view' | 'edit';

	addNewBody(): void;
	selectedCustomer: Customer | null;
}

export default class DetailStateClass implements DetailState {
	modal = $state<HTMLDialogElement>();
	body = $state<Kendaraan>({
		id_kendaraan: '',
		nomor_polisi: '',
		pemilik: {
			id_customer: '',
			nama: '',
			no_telp: '+62-',
			tipe_customer: 'Individu',
			keterangan: null,
			created_at: '',
			updated_at: null,
			deleted_at: null
		},
		nama_kendaraan: '',
		cc_kendaraan: 100,
		tahun_kendaraan: new Date().getFullYear(),
		keterangan: null,
		created_at: '',
		updated_at: null,
		deleted_at: null
	});
	mode = $state<'add' | 'view' | 'edit'>('add');
	selectedCustomer = $state<Customer | null>(null);

	addNewBody() {
		this.body = {
			id_kendaraan: '',
			nomor_polisi: '',
			pemilik: {
				id_customer: '',
				nama: '',
				no_telp: '+62-',
				tipe_customer: 'Individu',
				keterangan: null,
				created_at: '',
				updated_at: null,
				deleted_at: null
			},
			nama_kendaraan: '',
			cc_kendaraan: 100,
			tahun_kendaraan: new Date().getFullYear(),
			keterangan: null,
			created_at: '',
			updated_at: null,
			deleted_at: null
		};
		this.selectedCustomer = null;
	}
}

const DETAIL_STATE_KEY = Symbol('@@detail-state@@');

export function setDetailState(): void {
	setContext(DETAIL_STATE_KEY, new DetailStateClass());
}

export function getDetailState(): DetailState {
	return getContext<DetailState>(DETAIL_STATE_KEY);
}
