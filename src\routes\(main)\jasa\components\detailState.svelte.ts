import { _Jasa, type Jasa } from '$lib/schema/general';
import { getContext, setContext } from 'svelte';

export interface DetailState {
	modal: HTMLDialogElement | undefined;
	body: Jasa;
	mode: 'add' | 'view' | 'edit';

	addNewBody(): void;
}

export default class DetailStateClass implements DetailState {
	modal = $state<HTMLDialogElement>();
	body = $state<Jasa>(_Jasa);
	mode = $state<'add' | 'view' | 'edit'>('add');

	addNewBody() {
		this.body = _Jasa;
	}
}

const DETAIL_STATE_KEY = Symbol('@@detail-state@@');

export function setDetailState(): void {
	setContext(DETAIL_STATE_KEY, new DetailStateClass());
}

export function getDetailState(): DetailState {
	return getContext<DetailState>(DETAIL_STATE_KEY);
}
