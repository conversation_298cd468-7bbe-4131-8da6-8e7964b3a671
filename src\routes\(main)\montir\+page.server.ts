import type { PageServerLoad } from './$types';
import type { Montir } from '$lib/schema/general';

import { retrieve_fetch } from '$lib/utils/fetch';
import { Effect } from 'effect';

export const load: PageServerLoad = async () => {
	const getMontir = retrieve_fetch<Montir[]>('/montir');
	const response = await Effect.runPromise(getMontir);

	const statistics = {
		aktif: response.data.filter((montir) => montir.status === 'Aktif').length,
		non_aktif: response.data.filter((montir) => montir.status === 'Nonaktif').length,
		tunggu: response.data.filter((montir) => montir.status === 'Tunggu').length
	};

	return { list: response.data, total_rows: response.total_rows, statistics };
};
