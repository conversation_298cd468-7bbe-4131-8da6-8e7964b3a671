import type { Actions, PageServerLoad } from './$types';
import { KendaraanSchema, type Kendaraan, type KendaraanEncoded } from '$lib/schema/general';

import { Effect } from 'effect';
import { launch, retrieve } from '$lib/utils/fetch';
import { decodeForm } from '$lib/utils/validation';
import { fail } from '@sveltejs/kit';

export const load: PageServerLoad = async ({ fetch, url }) => {
	const limit = url.searchParams.get('limit') ?? '20';
	const offset = url.searchParams.get('offset') ?? '1';
	const keyword = url.searchParams.get('keyword') ?? '';

	const getKendaraan = retrieve<Kendaraan[]>(
		fetch,
		`/kendaraan?limit=${limit}&offset=${offset}&keyword=${keyword}`
	);
	const list = await Effect.runPromise(getKendaraan);

	return { list: list.data, total_rows: list.total_rows };
};

export const actions: Actions = {
	'action:kendaraan': async ({ request, fetch }) => {
		const data = await request.formData();
		const kendaraan = JSON.parse(data.get('kendaraan') as string) as Kendaraan;
		const mode = data.get('mode');

		const decoded = decodeForm<Kendaraan, KendaraanEncoded>(KendaraanSchema, kendaraan);
		if ('error' in decoded) return fail(400, { errors: decoded.errors });

		const action = launch(fetch, '/kendaraan', {
			method: mode === 'add' ? 'POST' : 'PUT',
			body: JSON.stringify({ ...kendaraan, id_pemilik: kendaraan.pemilik.id_customer })
		});

		const response = Effect.runPromise(action);
		return response;
	},
	'delete:kendaraan': async ({ request, fetch }) => {
		const data = await request.formData();
		const id = data.get('id');

		const action = launch(fetch, `/kendaraan/${id}`, {
			method: 'DELETE'
		});

		const response = Effect.runPromise(action);
		return response;
	}
};
