import { form } from '$app/server';
import {
	type KategoriSparepart,
	type KategoriSparepartEncoded,
	KategoriSparepartSchema
} from '$lib/schema/general';
import { launch_fetch } from '$lib/utils/fetch';
import { decodeForm } from '$lib/utils/validation';
import { fail, isActionFailure } from '@sveltejs/kit';
import { Effect } from 'effect';

export const createCategory = form(async (data) => {
	const kategori_sparepart = data.get('kategori_sparepart') as string;

	const decoded = decodeForm<KategoriSparepart, KategoriSparepartEncoded>(
		KategoriSparepartSchema,
		kategori_sparepart
	);

	if ('error' in decoded) return { errors: decoded.errors, message: 'Wrong Form Input' };

	const post = launch_fetch('/kategorisparepart', {
		method: 'POST',
		body: JSON.stringify(decoded)
	});

	const response = await Effect.runPromise(post);

	if (isActionFailure(response) && 'errors' in response && 'message' in response)
		return { errors: response.errors, message: response.message };

	return response;
});
