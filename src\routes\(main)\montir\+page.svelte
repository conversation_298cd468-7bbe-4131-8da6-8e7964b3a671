<script lang="ts">
	import PaginationUsingParam from '$lib/table/Pagination_Using_Param.svelte';
	import SearchUsingParam from '$lib/table/Search_Using_Param.svelte';
	import Table from '$lib/table/Table.svelte';
	import Icon from '@iconify/svelte';

	const { data } = $props();
</script>

<div class="stats shadow">
	<div class="stat text-success">
		<div class="stat-title">Karyawan Aktif</div>
		<div class="stat-value">{data.statistics.aktif}</div>
	</div>
	<div class="stat text-error">
		<div class="stat-title">Karyawan Non Aktif</div>
		<div class="stat-value">{data.statistics.non_aktif}</div>
	</div>
</div>

<div class="divider"></div>

<div class="flex justify-around gap-2">
	<button class="btn btn-primary btn-sm btn-outline">
		<Icon icon="mdi:plus" /> Tambah Montir
	</button>

	<SearchUsingParam placeholder="Search..." />

	<button class="btn btn-primary btn-sm btn-outline">
		<Icon icon="mdi:sort-alphabetical-ascending" />
		Sort By Ascending
	</button>
</div>

<br />

<Table
	table_header={[
		['numbering', 'No.'],
		['nama', 'Nama'],
		['custom', 'Spesialisasi'],
		['no_telp', 'No. Telp'],
		['custom', 'Status'],
		['custom', 'Riwayat'],
		['custom', 'Actions']
	]}
	table_data={data.list}
>
	{#snippet custom({ header, body })}
		{#if header === 'Spesialisasi'}
			{body.spesialisasi.nama}
		{:else if header === 'Status'}
			{#if body.status === 'Aktif'}
				<div class="badge badge-success text-[.75em] font-semibold text-white uppercase">
					{body.status}
				</div>
			{:else if body.status === 'Nonaktif'}
				<div class="badge badge-error text-[.75em] font-semibold text-white uppercase">
					{body.status}
				</div>
			{:else if body.status === 'Tunggu'}
				<div class="badge badge-warning text-[.75em] font-semibold text-white uppercase">
					{body.status}
				</div>
			{/if}
		{:else if header === 'Riwayat'}
			<div class="flex items-center gap-2">
				<a href="/montir/riwayat/{body.id_montir}" class="text-nowrap">
					<button class="btn btn-sm btn-outline btn-primary uppercase">
						<Icon icon="mdi:history" />
						Riwayat
					</button>
				</a>
			</div>

			{:else if header === 'K'}
		{/if}
	{/snippet}
</Table>

<br />

<div class="flex justify-end">
	<PaginationUsingParam total_content={data.list.length} />
</div>
