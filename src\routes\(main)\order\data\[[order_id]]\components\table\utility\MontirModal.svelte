<script lang="ts">
	import type { Jasa, Montir } from '$lib/schema/general';

	import {
		getUtilityModalState,
		setUtilityModalState
	} from '$lib/utils/utility/utilityModalState.svelte';

	import UtilityModal from '$lib/utils/utility/UtilityModal.svelte';
	import { getInvoiceState } from '../../../serviceOrder/InvoiceState.svelte';
	import { getToastState } from '$lib/utils/toast/ToastState.svelte';

	import Icon from '@iconify/svelte';

	setUtilityModalState();
	const utilityModalState = getUtilityModalState();
	const invoiceState = getInvoiceState();
	const toastState = getToastState();

	interface IProps {
		groupIndex: number;
		index: number;
		isSelected: boolean | null;
	}
	const { groupIndex, index, isSelected = false }: IProps = $props();
</script>

{#if !isSelected}
	<button
		class="btn btn-xs btn-outline btn-soft"
		onclick={() => utilityModalState.modal?.showModal()}
	>
		-- Pilih <PERSON> -- <Icon icon="majesticons:chevron-down" />
	</button>
{:else}
	<button
		class="btn btn-xs btn-outline btn-soft"
		onclick={() => utilityModalState.modal?.showModal()}
	>
		<Icon icon="majesticons:chevron-down" />
	</button>
{/if}

<UtilityModal url="/montir">
	{#snippet title()}
		<h2 class="font-semibold">Pilih Montir</h2>
	{/snippet}

	{#snippet item({ item }: { item: Montir })}
		<button
			class="btn btn-primary btn-sm btn-soft w-full justify-start"
			onclick={() => {
				invoiceState.service_order[groupIndex][index].montir = item;
				utilityModalState.modal?.close();
			}}
		>
			{item.nama} <span class="font-light"> ({item.id_montir})</span>
		</button>
	{/snippet}
</UtilityModal>
