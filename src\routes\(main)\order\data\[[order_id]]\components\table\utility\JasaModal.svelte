<script lang="ts">
	import type { Jasa } from '$lib/schema/general';

	import { rupiah } from '$lib/inputs/CurrencyState.svelte';
	import {
		getUtilityModalState,
		setUtilityModalState
	} from '$lib/utils/utility/utilityModalState.svelte';

	import UtilityModal from '$lib/utils/utility/UtilityModal.svelte';
	import { getInvoiceState } from '../../../serviceOrder/InvoiceState.svelte';
	import { getToastState } from '$lib/utils/toast/ToastState.svelte';

	import Icon from '@iconify/svelte';

	setUtilityModalState();
	const utilityModalState = getUtilityModalState();
	const invoiceState = getInvoiceState();
	const toastState = getToastState();

	const groupIndex = $derived(
		invoiceState.service_order.findIndex((service) =>
			service.find((order) => order.kind === 'jasa')
		)
	);

	const group = $derived(groupIndex === -1 ? [] : invoiceState.service_order[groupIndex]);

	interface IProps {
		onTable?: boolean;
	}
	const { onTable = false }: IProps = $props();
</script>

{#if onTable}
	<button
		class="btn btn-xs btn-outline btn-soft"
		onclick={() => utilityModalState.modal?.showModal()}
	>
		-- Tambah Jasa -- <Icon icon="majesticons:chevron-down" />
	</button>
{:else}
	<button class="btn btn-outline w-full" onclick={() => utilityModalState.modal?.showModal()}>
		Jasa
	</button>
{/if}

<UtilityModal url="/jasa">
	{#snippet title()}
		<h2 class="font-semibold">Pilih Jasa</h2>
	{/snippet}

	{#snippet item({ item }: { item: Jasa })}
		{@const alreadySelected = group.find(
			(order) => order.kind === 'jasa' && (order.data as Jasa).id_jasa === item.id_jasa
		)}
		<button
			class="btn btn-primary btn-sm btn-soft w-full justify-start"
			class:btn-disabled={alreadySelected}
			onclick={() => {
				if (alreadySelected) {
					toastState.add({
						message: 'Jasa sudah dipilih',
						type: 'error'
					});
					return;
				}

				const availableGroupIndex =
					groupIndex === -1
						? invoiceState.service_order.findIndex((service) => service.length === 0)
						: groupIndex;

				invoiceState.service_order[availableGroupIndex].push({
					kind: 'jasa',
					data: item,
					qty: 1,
					harga: item.harga,
					montir: null
				});
			}}
		>
			{item.nama_jasa} <span class="font-light"> @ {rupiah(item.harga)}</span>
		</button>
	{/snippet}
</UtilityModal>
