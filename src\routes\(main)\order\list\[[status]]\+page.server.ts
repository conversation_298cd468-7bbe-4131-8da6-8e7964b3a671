import type { PageServerLoad } from './$types';

import type { OrderStatus } from '$lib/schema/literal';
import type { TableHeader } from '$lib/table/types';
import type { Order } from '$lib/schema/order';

import { Effect } from 'effect';
import { retrieve } from '$lib/utils/fetch';
import { capitalize } from 'effect/String';

export const load: PageServerLoad = async ({ params, fetch }) => {
	const orderStatus = capitalize(params.status as string) as OrderStatus;

	const tableHeader: TableHeader<Order> = !orderStatus
		? [
				['numbering', 'No.'],
				['nomor_order', 'Nomor Order'],
				['created_at', 'Tanggal Order'],

				['custom', '<PERSON><PERSON>'],
				['custom', 'Status'],
				['custom', 'Actions']
			]
		: [
				['numbering', 'No.'],
				['nomor_order', 'Nomor Order'],
				['created_at', 'Tanggal Order'],

				['custom', '<PERSON><PERSON>'],
				['custom', 'Status'],
				['custom', 'Actions']
			];

	const fetchOrderList = retrieve<Order[]>(fetch, `/order/status/${orderStatus}`);
	const orderList = await Effect.runPromise(fetchOrderList);

	return { list: orderList.data, total_rows: orderList.total_rows, orderStatus, tableHeader };
};
