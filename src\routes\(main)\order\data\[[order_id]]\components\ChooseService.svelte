<script lang="ts">
	import type { ServiceType } from '$lib/schema/misc';
	import { capitalize } from 'effect/String';
	import { getInvoiceState, type CustomService } from '../serviceOrder/InvoiceState.svelte';
	import Icon from '@iconify/svelte';
	import Currency from '$lib/inputs/Currency.svelte';
	import JasaModal from './table/utility/JasaModal.svelte';
	import SparepartModal from './table/utility/SparepartModal.svelte';
	import PaketModal from './table/utility/PaketModal.svelte';

	let modal = $state<HTMLDialogElement>();

	let addService = $state<CustomService>({
		nama: '',
		harga: 0
	});

	interface IProps {
		mode?: ServiceType | 'general' | 'add';
		title?: string;
	}

	const { mode: initialMode = 'general', title = 'Pilih Service' }: IProps = $props();

	let mode = $state<ServiceType | 'general' | 'add'>(initialMode);

	const invoiceState = getInvoiceState();
</script>

<button
	class="btn {initialMode === 'general' ? 'btn-sm' : 'btn-xs'} btn-outline btn-soft"
	onclick={() => {
		if (initialMode !== 'general') mode = initialMode;
		modal?.showModal();
	}}
>
	-- {title} -- <Icon icon="majesticons:chevron-down" />
</button>

<dialog class="modal" bind:this={modal}>
	<div class="modal-box text-primary">
		<div class="mb-4 flex items-center justify-between gap-2 border-b border-b-gray-300 pb-2">
			{#if mode === 'general'}
				<h3 class="text-lg font-bold">Pilih Service</h3>
				<form method="dialog">
					<button class="btn btn-sm btn-circle btn-ghost">✕</button>
				</form>
			{:else if mode !== 'add'}
				<h3 class="text-lg font-bold">Pilih {capitalize(mode)}</h3>
				{#if initialMode === 'general'}
					<button class="btn btn-sm btn-circle btn-ghost" onclick={() => (mode = 'general')}>
						<Icon icon="majesticons:arrow-left" font-size="1.2em" />
					</button>
				{/if}
			{:else if mode === 'add'}
				<h3 class="text-lg font-bold">Tambah Service Manual</h3>
				<button class="btn btn-sm btn-circle btn-ghost" onclick={() => (mode = 'general')}>
					<Icon icon="majesticons:arrow-left" font-size="1.2em" />
				</button>
			{/if}
		</div>

		{#if mode === 'general'}
			<div class="flex flex-col gap-2">
				<PaketModal />
				<JasaModal />
				<SparepartModal />
				<button class="btn btn-outline btn-soft w-full" onclick={() => (mode = 'add')}>
					<Icon icon="majesticons:applications-add" /> Tambah Service
				</button>
			</div>
		{:else if mode === 'add'}
			<label class="floating-label">
				<span>Input Service</span>
				<input
					type="text"
					name="input_service"
					id="input_service"
					class="input w-full"
					placeholder="Service"
					bind:value={addService.nama}
				/>
			</label>

			<br />

			<label class="floating-label">
				<span>Harga Service</span>
				<Currency
					name="harga_service"
					id="harga_service"
					bind:value={addService.harga}
					class="w-full"
				/>
			</label>
		{/if}

		<div class="modal-action">
			{#if mode === 'add'}
				{@const groupIndex = invoiceState.service_order.findIndex((service) =>
					service.find((order) => order.kind === 'custom')
				)}

				{@const availableGroupIndex =
					groupIndex === -1
						? invoiceState.service_order.findIndex((service) => service.length === 0)
						: groupIndex}

				<button
					class="btn btn-primary"
					onclick={() => {
						invoiceState.service_order[availableGroupIndex].push({
							kind: 'custom',
							data: addService,
							qty: 1,
							harga: addService.harga,
							montir: null
						});
					}}
				>
					Simpan
				</button>
			{/if}
			<form method="dialog">
				<button class="btn" onclick={() => (mode = 'general')}>Close</button>
			</form>
		</div>
	</div>
</dialog>
