<script lang="ts">
	import Table from '$lib/table/Table.svelte';

	import JenisService from './table/JenisService.svelte';
	import Harga from './table/Harga.svelte';
	import Qty from './table/Qty.svelte';
	import HargaSatuan from './table/HargaSatuan.svelte';

	import Montir from './table/Montir.svelte';
	import { getInvoiceState } from '../serviceOrder/InvoiceState.svelte';

	const invoiceState = getInvoiceState();
</script>

<Table
	table_header={[
		['numbering', 'No.'],
		['custom', 'Jenis Service'],
		['custom', 'Harga Satuan'],
		['custom', 'QTY'],
		['custom', 'Montir'],
		['custom', 'Harga']
	]}
	table_data={invoiceState.service_order}
>
	{#snippet custom({ header, body, index })}
		{#if header === 'Jenis Service'}
			<JenisService {body} />
		{:else if header === 'QTY'}
			<Qty {body} {index} />
		{:else if header === 'Harga Satuan'}
			<HargaSatuan {body} {index} />
		{:else if header === 'Montir'}
			<Montir {body} {index} />
		{:else if header === 'Harga'}
			<Harga {body} />
		{/if}
	{/snippet}
</Table>
