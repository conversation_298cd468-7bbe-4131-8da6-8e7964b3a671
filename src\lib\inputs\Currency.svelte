<script lang="ts">
	import { cn } from '$lib/utils';
	import { formatCurrency, numeric, rupiah } from './CurrencyState.svelte';

	interface Props {
		name: string;
		id: string;
		value: number;
		class?: string;
		readonly?: boolean;
		disabled?: boolean;
	}
	let {
		name,
		id,
		value = $bindable(0),
		class: className,
		readonly = $bindable(false),
		disabled = $bindable(false)
	}: Props = $props();

	class Currency {
		#display = $state(rupiah(value));

		constructor() {
			this.#display = rupiah(value);
		}

		get display() {
			return this.#display;
		}

		set display(v) {
			this.#display = v;
			value = numeric(v);
		}
	}

	let currency = $state(new Currency());
	$effect(() => {
		currency = new Currency();
	});
</script>

<input type="hidden" {name} bind:value />
<input
	class={cn('input', className ?? '')}
	type="text"
	{id}
	bind:value={currency.display}
	onkeyup={(e) => formatCurrency(e.target as HTMLInputElement)}
	onblur={(e) => formatCurrency(e.target as HTMLInputElement, 'blur')}
	{readonly}
	{disabled}
/>
