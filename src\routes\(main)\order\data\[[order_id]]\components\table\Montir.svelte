<script lang="ts">
	import type { Montir, Paket } from '$lib/schema/general';
	import { type Service } from '../../serviceOrder/InvoiceState.svelte';

	import MontirModal from './utility/MontirModal.svelte';

	interface IProps {
		body: Service[];
		index: number;
	}

	const { body, index: groupIndex }: IProps = $props();

	const kind = $derived(body[0]?.kind);
</script>

<div class="flex flex-col gap-1">
	<ol class="flex flex-col">
		{#each body as b, index}
			{@const montir = b.montir as Montir}
			{@const isSelected = !!montir}

			<li class="mb-1 flex items-center gap-2">
				{#if isSelected}
					<div>&bullet; {montir.nama} ({montir.id_montir})</div>
				{/if}
				<MontirModal {groupIndex} {index} {isSelected} />
			</li>
		{/each}
	</ol>

	{#if kind === 'jasa' || kind === 'sparepart'}
		<p>&nbsp;</p>
	{/if}
</div>
