<script lang="ts">
	import type { Mutable } from 'effect/Types';
	import Icon from '@iconify/svelte';
	import FormField from '$lib/utils/formField/FormField.svelte';
	import { _KategoriSparepart, type KategoriSparepart } from '$lib/schema/general';
	import { createCategory } from './category.remote';
	import { setValidationErrorState } from '$lib/utils/validation/ValidationErrorState.svelte';

	let modal = $state<HTMLDialogElement>();

	let kategori = $state<Mutable<KategoriSparepart>>(_KategoriSparepart);

	setValidationErrorState();
</script>

<button class="btn btn-outline btn-primary btn-xs my-1" onclick={() => modal?.showModal()}>
	<Icon icon="mdi:plus" /> Tambah Kategori
</button>

<dialog class="modal" bind:this={modal}>
	<div class="modal-box text-primary">
		<div class="flex items-center justify-between">
			<h3 class="text-lg font-bold">Tambah Kategori Sparepart</h3>
			<button class="btn btn-sm btn-circle btn-ghost" onclick={() => modal?.close()}>✕</button>
		</div>

		<br />

		<form
			{...createCategory.enhance(async ({ submit }) => {
				await submit();
			})}
		>
			<FormField
				name="id_kategori_sparepart"
				label="Kode Kategori"
				type="text"
				bind:value={kategori.id_kategori_sparepart}
			/>

			<br />

			<FormField
				name="nama_kategori"
				label="Nama Kategori"
				type="text"
				bind:value={kategori.nama_kategori}
			/>

			<br />

			<input type="hidden" name="kategori_sparepart" value={JSON.stringify(kategori)} />
			<button class="btn btn-primary btn-sm btn-soft w-full" type="submit"> Tambahkan </button>
		</form>
	</div>
</dialog>
