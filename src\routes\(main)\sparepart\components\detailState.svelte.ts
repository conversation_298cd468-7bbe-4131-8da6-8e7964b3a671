import {
	_Sparepart,
	type Sparepart,
	type KategoriSparepart,
	type Satuan
} from '$lib/schema/general';
import { getContext, setContext } from 'svelte';

export interface DetailState {
	modal: HTMLDialogElement | undefined;
	body: Sparepart;
	mode: 'add' | 'view' | 'edit';

	addNewBody(): void;

	kategoriSparepart: KategoriSparepart | null;
	satuanSparepart: Satuan | null;
}

export default class DetailStateClass implements DetailState {
	modal = $state<HTMLDialogElement>();
	body = $state<Sparepart>(_Sparepart);
	mode = $state<'add' | 'view' | 'edit'>('add');

	addNewBody() {
		this.body = _Sparepart;
	}

	kategoriSparepart = $state<KategoriSparepart | null>(null);
	satuanSparepart = $state<Satuan | null>(null);
}

const DETAIL_STATE_KEY = Symbol('@@detail-state@@');

export function setDetailState(): void {
	setContext(DETAIL_STATE_KEY, new DetailStateClass());
}

export function getDetailState(): DetailState {
	return getContext<DetailState>(DETAIL_STATE_KEY);
}
